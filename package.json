{"name": "ramstone-lusaka-solutions", "private": true, "version": "1.0.0", "description": "Professional car repair services and general supply solutions in Lusaka, Zambia", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:dev": "vite build --mode development", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-tooltip": "^1.1.4", "@tanstack/react-query": "^5.56.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.462.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "react-router-dom": "^6.26.2", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.9.0", "@tailwindcss/typography": "^0.5.15", "@types/node": "^22.5.5", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "postcss": "^8.4.47", "rollup": "^4.21.0", "tailwindcss": "^3.4.11", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.1"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "^4.21.0", "@rollup/rollup-darwin-x64": "^4.21.0", "@rollup/rollup-darwin-arm64": "^4.21.0", "@rollup/rollup-win32-x64-msvc": "^4.21.0"}, "keywords": ["car repair", "auto repair", "general supply", "Lusaka", "Zambia", "panel beating", "spray painting", "construction tools"], "author": "Ramstone Creative Solutions", "repository": {"type": "git", "url": "https://github.com/Tim0010/ramstone-lusaka-solutions.git"}, "homepage": "https://ramstone-lusaka-solutions.netlify.app"}